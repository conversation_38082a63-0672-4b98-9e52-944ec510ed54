<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام التحديث التلقائي للرواتب</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .test-card { margin-bottom: 20px; }
        .test-result { margin-top: 15px; }
        .feature-list { list-style: none; padding: 0; }
        .feature-list li { padding: 5px 0; }
        .feature-list li i { width: 20px; }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h3 class="mb-0">
                            <i class="fas fa-robot me-2"></i>
                            اختبار نظام التحديث التلقائي للرواتب
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h5><i class="fas fa-info-circle me-2"></i>نظرة عامة على النظام الجديد</h5>
                            <p>تم تطوير نظام شامل للتحديث التلقائي لرواتب الموظفين بناءً على سجل الحضور والانصراف الفعلي.</p>
                            
                            <div class="row">
                                <div class="col-md-4">
                                    <h6><i class="fas fa-money-bill-wave text-success me-2"></i>البدلات التلقائية</h6>
                                    <ul class="feature-list">
                                        <li><i class="fas fa-check text-success"></i> بدل السكن (ثابت أو نسبة)</li>
                                        <li><i class="fas fa-check text-success"></i> بدل المواصلات (حسب أيام الحضور)</li>
                                        <li><i class="fas fa-check text-success"></i> بدل الطعام (حسب أيام الحضور)</li>
                                        <li><i class="fas fa-check text-success"></i> بدلات أخرى</li>
                                    </ul>
                                </div>
                                <div class="col-md-4">
                                    <h6><i class="fas fa-trophy text-warning me-2"></i>الحوافز والمكافآت</h6>
                                    <ul class="feature-list">
                                        <li><i class="fas fa-check text-success"></i> حافز الأداء (مشروط بالحضور)</li>
                                        <li><i class="fas fa-check text-success"></i> الساعات الإضافية (تلقائي)</li>
                                        <li><i class="fas fa-check text-success"></i> حافز الانضباط (عدم التأخير)</li>
                                        <li><i class="fas fa-check text-success"></i> حافز الحضور المثالي</li>
                                    </ul>
                                </div>
                                <div class="col-md-4">
                                    <h6><i class="fas fa-minus-circle text-danger me-2"></i>الخصومات والاستقطاعات</h6>
                                    <ul class="feature-list">
                                        <li><i class="fas fa-check text-success"></i> التأمينات الاجتماعية</li>
                                        <li><i class="fas fa-check text-success"></i> ضريبة الدخل (متدرجة)</li>
                                        <li><i class="fas fa-check text-success"></i> خصم التأخير (بالدقيقة)</li>
                                        <li><i class="fas fa-check text-success"></i> خصم الغياب والانصراف المبكر</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- اختبار حساب البدلات -->
            <div class="col-md-6">
                <div class="card test-card">
                    <div class="card-header bg-success text-white">
                        <h5>اختبار حساب البدلات التلقائية</h5>
                    </div>
                    <div class="card-body">
                        <button type="button" class="btn btn-success" id="testAllowancesBtn">
                            <i class="fas fa-calculator me-2"></i>
                            اختبار حساب البدلات
                        </button>
                        <div id="allowancesTest" class="test-result"></div>
                    </div>
                </div>
            </div>

            <!-- اختبار حساب الحوافز -->
            <div class="col-md-6">
                <div class="card test-card">
                    <div class="card-header bg-warning text-dark">
                        <h5>اختبار حساب الحوافز والمكافآت</h5>
                    </div>
                    <div class="card-body">
                        <button type="button" class="btn btn-warning" id="testBonusesBtn">
                            <i class="fas fa-trophy me-2"></i>
                            اختبار حساب الحوافز
                        </button>
                        <div id="bonusesTest" class="test-result"></div>
                    </div>
                </div>
            </div>

            <!-- اختبار حساب الخصومات -->
            <div class="col-md-6">
                <div class="card test-card">
                    <div class="card-header bg-danger text-white">
                        <h5>اختبار حساب الخصومات والاستقطاعات</h5>
                    </div>
                    <div class="card-body">
                        <button type="button" class="btn btn-danger" id="testDeductionsBtn">
                            <i class="fas fa-minus-circle me-2"></i>
                            اختبار حساب الخصومات
                        </button>
                        <div id="deductionsTest" class="test-result"></div>
                    </div>
                </div>
            </div>

            <!-- اختبار التحديث التلقائي الشامل -->
            <div class="col-md-6">
                <div class="card test-card">
                    <div class="card-header bg-primary text-white">
                        <h5>اختبار التحديث التلقائي الشامل</h5>
                    </div>
                    <div class="card-body">
                        <button type="button" class="btn btn-primary" id="testAutoUpdateBtn">
                            <i class="fas fa-sync-alt me-2"></i>
                            اختبار التحديث التلقائي
                        </button>
                        <div id="autoUpdateTest" class="test-result"></div>
                    </div>
                </div>
            </div>

            <!-- اختبار دقة صافي الراتب -->
            <div class="col-md-12">
                <div class="card test-card">
                    <div class="card-header bg-info text-white">
                        <h5>اختبار دقة حساب صافي الراتب</h5>
                    </div>
                    <div class="card-body">
                        <button type="button" class="btn btn-info" id="testNetSalaryBtn">
                            <i class="fas fa-calculator me-2"></i>
                            اختبار دقة صافي الراتب
                        </button>
                        <div id="netSalaryTest" class="test-result"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/database.js"></script>
    <script src="assets/js/payroll.js"></script>
    <script>
        // Mock data for testing
        const mockEmployee = {
            id: 'test-emp-001',
            name: 'أحمد محمد',
            salary: 8000,
            housing_allowance_type: 'percentage',
            housing_allowance_percentage: 25,
            daily_transport_allowance: 50,
            daily_food_allowance: 30,
            performance_bonus: 500,
            punctuality_bonus: 200,
            attendance_bonus: 300,
            late_deduction_rate: 10, // 10 ريال للساعة
            early_leave_deduction_rate: 15
        };

        const mockAttendance = [
            { date: '2024-01-01', status: 'present', check_in: '08:00', check_out: '17:00' },
            { date: '2024-01-02', status: 'late', check_in: '08:30', check_out: '17:00', late_minutes: 30 },
            { date: '2024-01-03', status: 'present', check_in: '08:00', check_out: '17:00', overtime_hours: 2 },
            { date: '2024-01-04', status: 'early_leave', check_in: '08:00', check_out: '16:30', early_leave_minutes: 30 },
            { date: '2024-01-05', status: 'absent' }
        ];

        // Initialize PayrollManager for testing
        let payrollManager;
        
        document.addEventListener('DOMContentLoaded', function() {
            // Mock window.samApp if not available
            if (typeof window.samApp === 'undefined') {
                window.samApp = {
                    formatCurrency: function(amount) {
                        return `${parseFloat(amount || 0).toLocaleString('ar-SA')} ر.س`;
                    },
                    showAlert: function(message, type) {
                        console.log(`Alert (${type}): ${message}`);
                        return { close: function() {} };
                    }
                };
            }

            // Initialize PayrollManager
            if (typeof PayrollManager !== 'undefined') {
                payrollManager = new PayrollManager();
            }

            // Bind test events
            bindTestEvents();
        });

        function bindTestEvents() {
            // Test allowances calculation
            document.getElementById('testAllowancesBtn').addEventListener('click', testAllowances);

            // Test bonuses calculation
            document.getElementById('testBonusesBtn').addEventListener('click', testBonuses);

            // Test deductions calculation
            document.getElementById('testDeductionsBtn').addEventListener('click', testDeductions);

            // Test auto update
            document.getElementById('testAutoUpdateBtn').addEventListener('click', testAutoUpdate);

            // Test net salary accuracy
            document.getElementById('testNetSalaryBtn').addEventListener('click', testNetSalary);
        }

        function testAllowances() {
            document.getElementById('allowancesTest').innerHTML = `
                <div class="alert alert-info">
                    <div class="spinner-border spinner-border-sm me-2"></div>
                    جاري اختبار حساب البدلات...
                </div>
            `;

            setTimeout(() => {
                try {
                    if (!payrollManager) {
                        throw new Error('PayrollManager غير متاح');
                    }

                    // Mock attendance stats
                    const attendanceStats = {
                        presentDays: 20,
                        totalWorkingDays: 22,
                        attendanceRate: 91
                    };

                    const allowances = payrollManager.calculateAllowancesFromAttendance(
                        mockEmployee,
                        attendanceStats,
                        '2024-01'
                    );

                    const expectedHousing = mockEmployee.salary * 0.25; // 25%
                    const expectedTransport = mockEmployee.daily_transport_allowance * attendanceStats.presentDays;
                    const expectedFood = mockEmployee.daily_food_allowance * attendanceStats.presentDays;

                    let testsPassed = 0;
                    let totalTests = 4;
                    let results = [];

                    // Test housing allowance
                    if (Math.abs(allowances.housing - expectedHousing) < 0.01) {
                        testsPassed++;
                        results.push('✅ بدل السكن: ' + window.samApp.formatCurrency(allowances.housing));
                    } else {
                        results.push('❌ بدل السكن: متوقع ' + window.samApp.formatCurrency(expectedHousing) +
                                   ' لكن حُسب ' + window.samApp.formatCurrency(allowances.housing));
                    }

                    // Test transport allowance
                    if (Math.abs(allowances.transport - expectedTransport) < 0.01) {
                        testsPassed++;
                        results.push('✅ بدل المواصلات: ' + window.samApp.formatCurrency(allowances.transport));
                    } else {
                        results.push('❌ بدل المواصلات: متوقع ' + window.samApp.formatCurrency(expectedTransport) +
                                   ' لكن حُسب ' + window.samApp.formatCurrency(allowances.transport));
                    }

                    // Test food allowance
                    if (Math.abs(allowances.food - expectedFood) < 0.01) {
                        testsPassed++;
                        results.push('✅ بدل الطعام: ' + window.samApp.formatCurrency(allowances.food));
                    } else {
                        results.push('❌ بدل الطعام: متوقع ' + window.samApp.formatCurrency(expectedFood) +
                                   ' لكن حُسب ' + window.samApp.formatCurrency(allowances.food));
                    }

                    // Test total
                    const expectedTotal = expectedHousing + expectedTransport + expectedFood;
                    if (Math.abs(allowances.total - expectedTotal) < 0.01) {
                        testsPassed++;
                        results.push('✅ إجمالي البدلات: ' + window.samApp.formatCurrency(allowances.total));
                    } else {
                        results.push('❌ إجمالي البدلات: متوقع ' + window.samApp.formatCurrency(expectedTotal) +
                                   ' لكن حُسب ' + window.samApp.formatCurrency(allowances.total));
                    }

                    const alertClass = testsPassed === totalTests ? 'success' :
                                     testsPassed >= totalTests / 2 ? 'warning' : 'danger';

                    document.getElementById('allowancesTest').innerHTML = `
                        <div class="alert alert-${alertClass}">
                            <h6><i class="fas fa-${testsPassed === totalTests ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
                            نتيجة اختبار البدلات: ${testsPassed}/${totalTests}</h6>
                            ${results.map(r => `<small>${r}</small>`).join('<br>')}
                        </div>
                    `;

                } catch (error) {
                    document.getElementById('allowancesTest').innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-times-circle me-2"></i>
                            خطأ في اختبار البدلات: ${error.message}
                        </div>
                    `;
                }
            }, 1000);
        }

        function testBonuses() {
            document.getElementById('bonusesTest').innerHTML = `
                <div class="alert alert-info">
                    <div class="spinner-border spinner-border-sm me-2"></div>
                    جاري اختبار حساب الحوافز...
                </div>
            `;

            setTimeout(() => {
                try {
                    if (!payrollManager) {
                        throw new Error('PayrollManager غير متاح');
                    }

                    const attendanceStats = {
                        presentDays: 22,
                        totalWorkingDays: 22,
                        attendanceRate: 100,
                        lateCount: 0
                    };

                    const bonuses = payrollManager.calculateBonusesFromAttendance(
                        mockEmployee,
                        mockAttendance,
                        attendanceStats,
                        '2024-01'
                    );

                    let testsPassed = 0;
                    let totalTests = 4;
                    let results = [];

                    // Test performance bonus (100% attendance)
                    if (bonuses.performance === mockEmployee.performance_bonus) {
                        testsPassed++;
                        results.push('✅ حافز الأداء: ' + window.samApp.formatCurrency(bonuses.performance));
                    } else {
                        results.push('❌ حافز الأداء: متوقع ' + window.samApp.formatCurrency(mockEmployee.performance_bonus) +
                                   ' لكن حُسب ' + window.samApp.formatCurrency(bonuses.performance));
                    }

                    // Test punctuality bonus (no late)
                    if (bonuses.punctuality === mockEmployee.punctuality_bonus) {
                        testsPassed++;
                        results.push('✅ حافز الانضباط: ' + window.samApp.formatCurrency(bonuses.punctuality));
                    } else {
                        results.push('❌ حافز الانضباط: متوقع ' + window.samApp.formatCurrency(mockEmployee.punctuality_bonus) +
                                   ' لكن حُسب ' + window.samApp.formatCurrency(bonuses.punctuality));
                    }

                    // Test attendance bonus (100% attendance)
                    if (bonuses.attendance === mockEmployee.attendance_bonus) {
                        testsPassed++;
                        results.push('✅ حافز الحضور: ' + window.samApp.formatCurrency(bonuses.attendance));
                    } else {
                        results.push('❌ حافز الحضور: متوقع ' + window.samApp.formatCurrency(mockEmployee.attendance_bonus) +
                                   ' لكن حُسب ' + window.samApp.formatCurrency(bonuses.attendance));
                    }

                    // Test overtime calculation
                    if (bonuses.overtime > 0) {
                        testsPassed++;
                        results.push('✅ الساعات الإضافية: ' + window.samApp.formatCurrency(bonuses.overtime) +
                                   ' (' + bonuses.overtimeHours + ' ساعة)');
                    } else {
                        results.push('⚠️ الساعات الإضافية: لم يتم حسابها');
                    }

                    const alertClass = testsPassed === totalTests ? 'success' :
                                     testsPassed >= totalTests / 2 ? 'warning' : 'danger';

                    document.getElementById('bonusesTest').innerHTML = `
                        <div class="alert alert-${alertClass}">
                            <h6><i class="fas fa-${testsPassed === totalTests ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
                            نتيجة اختبار الحوافز: ${testsPassed}/${totalTests}</h6>
                            ${results.map(r => `<small>${r}</small>`).join('<br>')}
                            <br><small><strong>إجمالي الحوافز: ${window.samApp.formatCurrency(bonuses.total)}</strong></small>
                        </div>
                    `;

                } catch (error) {
                    document.getElementById('bonusesTest').innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-times-circle me-2"></i>
                            خطأ في اختبار الحوافز: ${error.message}
                        </div>
                    `;
                }
            }, 1000);
        }

        function testDeductions() {
            document.getElementById('deductionsTest').innerHTML = `
                <div class="alert alert-info">
                    <div class="spinner-border spinner-border-sm me-2"></div>
                    جاري اختبار حساب الخصومات...
                </div>
            `;

            setTimeout(() => {
                try {
                    if (!payrollManager) {
                        throw new Error('PayrollManager غير متاح');
                    }

                    const attendanceStats = {
                        presentDays: 20,
                        absentDays: 2,
                        totalWorkingDays: 22,
                        lateCount: 1,
                        earlyLeaveCount: 1
                    };

                    const grossSalary = 10000; // مثال

                    const deductions = payrollManager.calculateDeductionsFromAttendance(
                        mockEmployee,
                        mockAttendance,
                        attendanceStats,
                        '2024-01',
                        grossSalary
                    );

                    let testsPassed = 0;
                    let totalTests = 5;
                    let results = [];

                    // Test social insurance (9%)
                    const expectedSocialInsurance = grossSalary * 0.09;
                    if (Math.abs(deductions.socialInsurance - expectedSocialInsurance) < 0.01) {
                        testsPassed++;
                        results.push('✅ التأمينات الاجتماعية: ' + window.samApp.formatCurrency(deductions.socialInsurance));
                    } else {
                        results.push('❌ التأمينات الاجتماعية: متوقع ' + window.samApp.formatCurrency(expectedSocialInsurance) +
                                   ' لكن حُسب ' + window.samApp.formatCurrency(deductions.socialInsurance));
                    }

                    // Test income tax
                    if (deductions.incomeTax >= 0) {
                        testsPassed++;
                        results.push('✅ ضريبة الدخل: ' + window.samApp.formatCurrency(deductions.incomeTax));
                    } else {
                        results.push('❌ ضريبة الدخل: قيمة غير صحيحة');
                    }

                    // Test lateness deduction
                    if (deductions.lateness >= 0) {
                        testsPassed++;
                        results.push('✅ خصم التأخير: ' + window.samApp.formatCurrency(deductions.lateness));
                    } else {
                        results.push('❌ خصم التأخير: قيمة غير صحيحة');
                    }

                    // Test absence deduction
                    if (deductions.absence >= 0) {
                        testsPassed++;
                        results.push('✅ خصم الغياب: ' + window.samApp.formatCurrency(deductions.absence));
                    } else {
                        results.push('❌ خصم الغياب: قيمة غير صحيحة');
                    }

                    // Test early leave deduction
                    if (deductions.earlyLeave >= 0) {
                        testsPassed++;
                        results.push('✅ خصم الانصراف المبكر: ' + window.samApp.formatCurrency(deductions.earlyLeave));
                    } else {
                        results.push('❌ خصم الانصراف المبكر: قيمة غير صحيحة');
                    }

                    const alertClass = testsPassed === totalTests ? 'success' :
                                     testsPassed >= totalTests / 2 ? 'warning' : 'danger';

                    document.getElementById('deductionsTest').innerHTML = `
                        <div class="alert alert-${alertClass}">
                            <h6><i class="fas fa-${testsPassed === totalTests ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
                            نتيجة اختبار الخصومات: ${testsPassed}/${totalTests}</h6>
                            ${results.map(r => `<small>${r}</small>`).join('<br>')}
                            <br><small><strong>إجمالي الخصومات: ${window.samApp.formatCurrency(deductions.total)}</strong></small>
                        </div>
                    `;

                } catch (error) {
                    document.getElementById('deductionsTest').innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-times-circle me-2"></i>
                            خطأ في اختبار الخصومات: ${error.message}
                        </div>
                    `;
                }
            }, 1000);
        }

        function testAutoUpdate() {
            document.getElementById('autoUpdateTest').innerHTML = `
                <div class="alert alert-info">
                    <div class="spinner-border spinner-border-sm me-2"></div>
                    جاري اختبار التحديث التلقائي...
                </div>
            `;

            setTimeout(() => {
                try {
                    if (!payrollManager) {
                        throw new Error('PayrollManager غير متاح');
                    }

                    // Test if auto update functions exist
                    let testsPassed = 0;
                    let totalTests = 4;
                    let results = [];

                    // Test calculatePayrollFromAttendance
                    if (typeof payrollManager.calculatePayrollFromAttendance === 'function') {
                        testsPassed++;
                        results.push('✅ دالة حساب الراتب من الحضور موجودة');
                    } else {
                        results.push('❌ دالة حساب الراتب من الحضور غير موجودة');
                    }

                    // Test autoUpdateAllPayrolls
                    if (typeof payrollManager.autoUpdateAllPayrolls === 'function') {
                        testsPassed++;
                        results.push('✅ دالة التحديث التلقائي للكل موجودة');
                    } else {
                        results.push('❌ دالة التحديث التلقائي للكل غير موجودة');
                    }

                    // Test autoUpdateSinglePayroll
                    if (typeof payrollManager.autoUpdateSinglePayroll === 'function') {
                        testsPassed++;
                        results.push('✅ دالة التحديث التلقائي الفردي موجودة');
                    } else {
                        results.push('❌ دالة التحديث التلقائي الفردي غير موجودة');
                    }

                    // Test UI integration
                    const autoUpdateBtn = document.getElementById('autoUpdateAllBtn');
                    if (autoUpdateBtn) {
                        testsPassed++;
                        results.push('✅ زر التحديث التلقائي موجود في الواجهة');
                    } else {
                        results.push('❌ زر التحديث التلقائي غير موجود في الواجهة');
                    }

                    const alertClass = testsPassed === totalTests ? 'success' :
                                     testsPassed >= totalTests / 2 ? 'warning' : 'danger';

                    document.getElementById('autoUpdateTest').innerHTML = `
                        <div class="alert alert-${alertClass}">
                            <h6><i class="fas fa-${testsPassed === totalTests ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
                            نتيجة اختبار التحديث التلقائي: ${testsPassed}/${totalTests}</h6>
                            ${results.map(r => `<small>${r}</small>`).join('<br>')}
                        </div>
                    `;

                } catch (error) {
                    document.getElementById('autoUpdateTest').innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-times-circle me-2"></i>
                            خطأ في اختبار التحديث التلقائي: ${error.message}
                        </div>
                    `;
                }
            }, 1000);
        }

        function testNetSalary() {
            document.getElementById('netSalaryTest').innerHTML = `
                <div class="alert alert-info">
                    <div class="spinner-border spinner-border-sm me-2"></div>
                    جاري اختبار دقة صافي الراتب...
                </div>
            `;

            setTimeout(() => {
                try {
                    if (!payrollManager) {
                        throw new Error('PayrollManager غير متاح');
                    }

                    // Mock complete payroll calculation
                    const basicSalary = 8000;
                    const allowances = { total: 3000 };
                    const bonuses = { total: 1000 };
                    const deductions = { total: 1500 };

                    const grossSalary = basicSalary + allowances.total + bonuses.total;
                    const expectedNetSalary = Math.max(0, grossSalary - deductions.total);

                    let testsPassed = 0;
                    let totalTests = 3;
                    let results = [];

                    // Test gross salary calculation
                    if (grossSalary === (basicSalary + allowances.total + bonuses.total)) {
                        testsPassed++;
                        results.push('✅ إجمالي الاستحقاقات: ' + window.samApp.formatCurrency(grossSalary));
                    } else {
                        results.push('❌ خطأ في حساب إجمالي الاستحقاقات');
                    }

                    // Test net salary calculation
                    if (expectedNetSalary === (grossSalary - deductions.total)) {
                        testsPassed++;
                        results.push('✅ صافي الراتب: ' + window.samApp.formatCurrency(expectedNetSalary));
                    } else {
                        results.push('❌ خطأ في حساب صافي الراتب');
                    }

                    // Test negative salary protection
                    const negativeTest = Math.max(0, 1000 - 2000);
                    if (negativeTest === 0) {
                        testsPassed++;
                        results.push('✅ الحماية من الراتب السالب تعمل بشكل صحيح');
                    } else {
                        results.push('❌ الحماية من الراتب السالب لا تعمل');
                    }

                    const alertClass = testsPassed === totalTests ? 'success' :
                                     testsPassed >= totalTests / 2 ? 'warning' : 'danger';

                    document.getElementById('netSalaryTest').innerHTML = `
                        <div class="alert alert-${alertClass}">
                            <h6><i class="fas fa-${testsPassed === totalTests ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
                            نتيجة اختبار دقة صافي الراتب: ${testsPassed}/${totalTests}</h6>
                            ${results.map(r => `<small>${r}</small>`).join('<br>')}
                            <hr>
                            <div class="row text-center">
                                <div class="col-3">
                                    <strong>الراتب الأساسي</strong><br>
                                    <span class="text-primary">${window.samApp.formatCurrency(basicSalary)}</span>
                                </div>
                                <div class="col-3">
                                    <strong>البدلات والحوافز</strong><br>
                                    <span class="text-success">+${window.samApp.formatCurrency(allowances.total + bonuses.total)}</span>
                                </div>
                                <div class="col-3">
                                    <strong>الخصومات</strong><br>
                                    <span class="text-danger">-${window.samApp.formatCurrency(deductions.total)}</span>
                                </div>
                                <div class="col-3">
                                    <strong>صافي الراتب</strong><br>
                                    <span class="text-info">${window.samApp.formatCurrency(expectedNetSalary)}</span>
                                </div>
                            </div>
                        </div>
                    `;

                } catch (error) {
                    document.getElementById('netSalaryTest').innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-times-circle me-2"></i>
                            خطأ في اختبار صافي الراتب: ${error.message}
                        </div>
                    `;
                }
            }, 1000);
        }
    </script>
</body>
</html>
