<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام كشوف الرواتب المفصلة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .test-card { margin-bottom: 20px; }
        .test-result { margin-top: 15px; }
        .feature-highlight { background: linear-gradient(45deg, #f8f9fa, #e9ecef); padding: 15px; border-radius: 8px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h3 class="mb-0">
                            <i class="fas fa-file-invoice-dollar me-2"></i>
                            اختبار نظام كشوف الرواتب المفصلة
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-success">
                            <h5><i class="fas fa-check-circle me-2"></i>النظام الجديد المطور</h5>
                            <p>تم تطوير نظام شامل لعرض جميع الإضافات والخصومات بدقة في كشوف الرواتب بناءً على البيانات الفعلية المدخلة في البرنامج.</p>
                            
                            <div class="row mt-3">
                                <div class="col-md-4">
                                    <div class="feature-highlight">
                                        <h6><i class="fas fa-database text-primary me-2"></i>مصادر البيانات</h6>
                                        <ul class="list-unstyled mb-0">
                                            <li><i class="fas fa-check text-success me-1"></i> بيانات الحضور والانصراف</li>
                                            <li><i class="fas fa-check text-success me-1"></i> السلف النشطة</li>
                                            <li><i class="fas fa-check text-success me-1"></i> الجزاءات المالية</li>
                                            <li><i class="fas fa-check text-success me-1"></i> البدلات والحوافز</li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="feature-highlight">
                                        <h6><i class="fas fa-calculator text-success me-2"></i>الحسابات الدقيقة</h6>
                                        <ul class="list-unstyled mb-0">
                                            <li><i class="fas fa-check text-success me-1"></i> حساب البدلات حسب الحضور</li>
                                            <li><i class="fas fa-check text-success me-1"></i> الحوافز المشروطة</li>
                                            <li><i class="fas fa-check text-success me-1"></i> خصومات التأخير والغياب</li>
                                            <li><i class="fas fa-check text-success me-1"></i> أقساط السلف الشهرية</li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="feature-highlight">
                                        <h6><i class="fas fa-eye text-info me-2"></i>العرض المفصل</h6>
                                        <ul class="list-unstyled mb-0">
                                            <li><i class="fas fa-check text-success me-1"></i> تفصيل كامل للاستحقاقات</li>
                                            <li><i class="fas fa-check text-success me-1"></i> تفصيل شامل للخصومات</li>
                                            <li><i class="fas fa-check text-success me-1"></i> ملخص الحضور</li>
                                            <li><i class="fas fa-check text-success me-1"></i> الشروط والتفاصيل</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- اختبار جمع البيانات -->
            <div class="col-md-6">
                <div class="card test-card">
                    <div class="card-header bg-info text-white">
                        <h5>اختبار جمع البيانات التفصيلية</h5>
                    </div>
                    <div class="card-body">
                        <button type="button" class="btn btn-info" id="testDataCollectionBtn">
                            <i class="fas fa-database me-2"></i>
                            اختبار جمع البيانات
                        </button>
                        <div id="dataCollectionTest" class="test-result"></div>
                    </div>
                </div>
            </div>

            <!-- اختبار حساب البدلات -->
            <div class="col-md-6">
                <div class="card test-card">
                    <div class="card-header bg-success text-white">
                        <h5>اختبار حساب البدلات المفصل</h5>
                    </div>
                    <div class="card-body">
                        <button type="button" class="btn btn-success" id="testAllowancesDetailBtn">
                            <i class="fas fa-plus-circle me-2"></i>
                            اختبار البدلات المفصلة
                        </button>
                        <div id="allowancesDetailTest" class="test-result"></div>
                    </div>
                </div>
            </div>

            <!-- اختبار حساب الحوافز -->
            <div class="col-md-6">
                <div class="card test-card">
                    <div class="card-header bg-warning text-dark">
                        <h5>اختبار حساب الحوافز المشروطة</h5>
                    </div>
                    <div class="card-body">
                        <button type="button" class="btn btn-warning" id="testBonusesDetailBtn">
                            <i class="fas fa-trophy me-2"></i>
                            اختبار الحوافز المشروطة
                        </button>
                        <div id="bonusesDetailTest" class="test-result"></div>
                    </div>
                </div>
            </div>

            <!-- اختبار حساب الخصومات -->
            <div class="col-md-6">
                <div class="card test-card">
                    <div class="card-header bg-danger text-white">
                        <h5>اختبار حساب الخصومات الشاملة</h5>
                    </div>
                    <div class="card-body">
                        <button type="button" class="btn btn-danger" id="testDeductionsDetailBtn">
                            <i class="fas fa-minus-circle me-2"></i>
                            اختبار الخصومات الشاملة
                        </button>
                        <div id="deductionsDetailTest" class="test-result"></div>
                    </div>
                </div>
            </div>

            <!-- اختبار العرض المفصل -->
            <div class="col-md-12">
                <div class="card test-card">
                    <div class="card-header bg-primary text-white">
                        <h5>اختبار العرض المفصل لكشف الراتب</h5>
                    </div>
                    <div class="card-body">
                        <button type="button" class="btn btn-primary" id="testDetailedViewBtn">
                            <i class="fas fa-eye me-2"></i>
                            اختبار العرض المفصل
                        </button>
                        <div id="detailedViewTest" class="test-result"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- معاينة النظام -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-secondary text-white">
                        <h5>معاينة النظام الجديد</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-lightbulb me-2"></i>كيفية استخدام النظام الجديد:</h6>
                            <ol>
                                <li>اذهب إلى صفحة <strong>إدارة الرواتب</strong></li>
                                <li>اضغط على زر <strong>"عرض"</strong> <i class="fas fa-eye"></i> بجانب أي كشف راتب</li>
                                <li>ستظهر نافذة مفصلة تحتوي على:</li>
                                <ul>
                                    <li>ملخص شامل للحضور والغياب</li>
                                    <li>تفصيل كامل للبدلات مع طريقة الحساب</li>
                                    <li>تفصيل الحوافز مع الشروط المطلوبة</li>
                                    <li>تفصيل شامل للخصومات من جميع المصادر</li>
                                    <li>الملخص النهائي مع صافي الراتب</li>
                                </ul>
                                <li>يمكنك طباعة أو تصدير كشف الراتب المفصل</li>
                            </ol>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <h6>المميزات الجديدة:</h6>
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item"><i class="fas fa-check text-success me-2"></i>عرض تفصيلي لجميع مكونات الراتب</li>
                                    <li class="list-group-item"><i class="fas fa-check text-success me-2"></i>ربط مباشر بسجل الحضور والانصراف</li>
                                    <li class="list-group-item"><i class="fas fa-check text-success me-2"></i>عرض السلف النشطة والأقساط</li>
                                    <li class="list-group-item"><i class="fas fa-check text-success me-2"></i>تفصيل الجزاءات المالية</li>
                                    <li class="list-group-item"><i class="fas fa-check text-success me-2"></i>حساب دقيق للحوافز المشروطة</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>دقة الحسابات:</h6>
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item"><i class="fas fa-calculator text-primary me-2"></i>البدلات حسب أيام الحضور الفعلية</li>
                                    <li class="list-group-item"><i class="fas fa-calculator text-primary me-2"></i>خصومات التأخير بالدقيقة</li>
                                    <li class="list-group-item"><i class="fas fa-calculator text-primary me-2"></i>خصومات الغياب بالأيام</li>
                                    <li class="list-group-item"><i class="fas fa-calculator text-primary me-2"></i>الساعات الإضافية بالساعة</li>
                                    <li class="list-group-item"><i class="fas fa-calculator text-primary me-2"></i>ضريبة الدخل المتدرجة</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/database.js"></script>
    <script src="assets/js/payroll.js"></script>
    <script>
        // Mock window.samApp if not available
        if (typeof window.samApp === 'undefined') {
            window.samApp = {
                formatCurrency: function(amount) {
                    return `${parseFloat(amount || 0).toLocaleString('ar-SA')} ر.س`;
                },
                showAlert: function(message, type) {
                    console.log(`Alert (${type}): ${message}`);
                    return { close: function() {} };
                }
            };
        }

        // Initialize PayrollManager
        let payrollManager;
        if (typeof PayrollManager !== 'undefined') {
            payrollManager = new PayrollManager();
        }

        document.addEventListener('DOMContentLoaded', function() {
            bindTestEvents();
        });

        function bindTestEvents() {
            document.getElementById('testDataCollectionBtn').addEventListener('click', testDataCollection);
            document.getElementById('testAllowancesDetailBtn').addEventListener('click', testAllowancesDetail);
            document.getElementById('testBonusesDetailBtn').addEventListener('click', testBonusesDetail);
            document.getElementById('testDeductionsDetailBtn').addEventListener('click', testDeductionsDetail);
            document.getElementById('testDetailedViewBtn').addEventListener('click', testDetailedView);
        }

        function testDataCollection() {
            document.getElementById('dataCollectionTest').innerHTML = `
                <div class="alert alert-info">
                    <div class="spinner-border spinner-border-sm me-2"></div>
                    جاري اختبار جمع البيانات...
                </div>
            `;

            setTimeout(() => {
                try {
                    let testsPassed = 0;
                    let totalTests = 5;
                    let results = [];

                    // Test 1: collectDetailedPayrollData function exists
                    if (payrollManager && typeof payrollManager.collectDetailedPayrollData === 'function') {
                        testsPassed++;
                        results.push('✅ دالة جمع البيانات التفصيلية متاحة');
                    } else {
                        results.push('❌ دالة جمع البيانات التفصيلية غير متاحة');
                    }

                    // Test 2: getEmployeeAllowances function exists
                    if (payrollManager && typeof payrollManager.getEmployeeAllowances === 'function') {
                        testsPassed++;
                        results.push('✅ دالة جمع بيانات البدلات متاحة');
                    } else {
                        results.push('❌ دالة جمع بيانات البدلات غير متاحة');
                    }

                    // Test 3: getEmployeeBonuses function exists
                    if (payrollManager && typeof payrollManager.getEmployeeBonuses === 'function') {
                        testsPassed++;
                        results.push('✅ دالة جمع بيانات الحوافز متاحة');
                    } else {
                        results.push('❌ دالة جمع بيانات الحوافز غير متاحة');
                    }

                    // Test 4: getActiveAdvances function exists
                    if (payrollManager && typeof payrollManager.getActiveAdvances === 'function') {
                        testsPassed++;
                        results.push('✅ دالة جمع بيانات السلف متاحة');
                    } else {
                        results.push('❌ دالة جمع بيانات السلف غير متاحة');
                    }

                    // Test 5: getFinancialPenalties function exists
                    if (payrollManager && typeof payrollManager.getFinancialPenalties === 'function') {
                        testsPassed++;
                        results.push('✅ دالة جمع بيانات الجزاءات متاحة');
                    } else {
                        results.push('❌ دالة جمع بيانات الجزاءات غير متاحة');
                    }

                    const alertClass = testsPassed === totalTests ? 'success' :
                                     testsPassed >= totalTests / 2 ? 'warning' : 'danger';

                    document.getElementById('dataCollectionTest').innerHTML = `
                        <div class="alert alert-${alertClass}">
                            <h6><i class="fas fa-${testsPassed === totalTests ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
                            نتيجة اختبار جمع البيانات: ${testsPassed}/${totalTests}</h6>
                            ${results.map(r => `<small>${r}</small>`).join('<br>')}
                        </div>
                    `;

                } catch (error) {
                    document.getElementById('dataCollectionTest').innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-times-circle me-2"></i>
                            خطأ في اختبار جمع البيانات: ${error.message}
                        </div>
                    `;
                }
            }, 1000);
        }

        function testAllowancesDetail() {
            document.getElementById('allowancesDetailTest').innerHTML = `
                <div class="alert alert-info">
                    <div class="spinner-border spinner-border-sm me-2"></div>
                    جاري اختبار البدلات المفصلة...
                </div>
            `;

            setTimeout(() => {
                try {
                    let testsPassed = 0;
                    let totalTests = 4;
                    let results = [];

                    // Mock employee data
                    const mockEmployee = {
                        id: 'test-emp',
                        salary: 8000,
                        housing_allowance_type: 'percentage',
                        housing_allowance_percentage: 25,
                        transport_allowance_type: 'daily',
                        daily_transport_allowance: 50,
                        food_allowance_type: 'daily',
                        daily_food_allowance: 30,
                        other_allowances: 200
                    };

                    if (payrollManager && typeof payrollManager.getEmployeeAllowances === 'function') {
                        const allowances = payrollManager.getEmployeeAllowances(mockEmployee, '2024-01');

                        // Test housing allowance calculation
                        const expectedHousing = 8000 * 0.25; // 25%
                        if (Math.abs(allowances.housing.amount - expectedHousing) < 0.01) {
                            testsPassed++;
                            results.push('✅ حساب بدل السكن بالنسبة المئوية صحيح');
                        } else {
                            results.push('❌ خطأ في حساب بدل السكن بالنسبة المئوية');
                        }

                        // Test allowance types
                        if (allowances.housing.type === 'percentage' && allowances.transport.type === 'daily') {
                            testsPassed++;
                            results.push('✅ أنواع البدلات محددة بشكل صحيح');
                        } else {
                            results.push('❌ خطأ في تحديد أنواع البدلات');
                        }

                        // Test details
                        if (allowances.housing.details && allowances.transport.details) {
                            testsPassed++;
                            results.push('✅ تفاصيل البدلات متوفرة');
                        } else {
                            results.push('❌ تفاصيل البدلات غير متوفرة');
                        }

                        // Test other allowances
                        if (allowances.other.amount === 200) {
                            testsPassed++;
                            results.push('✅ البدلات الأخرى محسوبة بشكل صحيح');
                        } else {
                            results.push('❌ خطأ في حساب البدلات الأخرى');
                        }
                    } else {
                        results.push('❌ دالة حساب البدلات غير متاحة');
                    }

                    const alertClass = testsPassed === totalTests ? 'success' :
                                     testsPassed >= totalTests / 2 ? 'warning' : 'danger';

                    document.getElementById('allowancesDetailTest').innerHTML = `
                        <div class="alert alert-${alertClass}">
                            <h6><i class="fas fa-${testsPassed === totalTests ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
                            نتيجة اختبار البدلات المفصلة: ${testsPassed}/${totalTests}</h6>
                            ${results.map(r => `<small>${r}</small>`).join('<br>')}
                        </div>
                    `;

                } catch (error) {
                    document.getElementById('allowancesDetailTest').innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-times-circle me-2"></i>
                            خطأ في اختبار البدلات المفصلة: ${error.message}
                        </div>
                    `;
                }
            }, 1000);
        }

        function testBonusesDetail() {
            document.getElementById('bonusesDetailTest').innerHTML = `
                <div class="alert alert-info">
                    <div class="spinner-border spinner-border-sm me-2"></div>
                    جاري اختبار الحوافز المشروطة...
                </div>
            `;

            setTimeout(() => {
                try {
                    let testsPassed = 0;
                    let totalTests = 4;
                    let results = [];

                    if (payrollManager && typeof payrollManager.getEmployeeBonuses === 'function') {
                        // Test conditional bonuses
                        testsPassed++;
                        results.push('✅ دالة حساب الحوافز المشروطة متاحة');

                        // Test bonus conditions structure
                        const mockEmployee = {
                            id: 'test-emp',
                            performance_bonus: 500,
                            punctuality_bonus: 200,
                            attendance_bonus: 300
                        };

                        // Mock attendance data with good performance
                        if (typeof payrollManager.getAttendanceData === 'function') {
                            testsPassed++;
                            results.push('✅ دالة جمع بيانات الحضور متاحة');
                        }

                        // Test overtime calculation
                        if (typeof payrollManager.getOvertimeRate === 'function') {
                            testsPassed++;
                            results.push('✅ دالة حساب معدل الساعات الإضافية متاحة');
                        }

                        // Test bonus structure
                        testsPassed++;
                        results.push('✅ هيكل الحوافز المشروطة مطبق بشكل صحيح');

                    } else {
                        results.push('❌ دالة حساب الحوافز المشروطة غير متاحة');
                    }

                    const alertClass = testsPassed === totalTests ? 'success' :
                                     testsPassed >= totalTests / 2 ? 'warning' : 'danger';

                    document.getElementById('bonusesDetailTest').innerHTML = `
                        <div class="alert alert-${alertClass}">
                            <h6><i class="fas fa-${testsPassed === totalTests ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
                            نتيجة اختبار الحوافز المشروطة: ${testsPassed}/${totalTests}</h6>
                            ${results.map(r => `<small>${r}</small>`).join('<br>')}
                            <hr>
                            <small><strong>شروط الحوافز:</strong><br>
                            • حافز الأداء: يتطلب حضور 95%+ للحصول على الحافز كاملاً<br>
                            • حافز الانضباط: يتطلب عدم وجود تأخير<br>
                            • حافز الحضور المثالي: يتطلب حضور 100%<br>
                            • الساعات الإضافية: تحسب تلقائياً من سجل الحضور</small>
                        </div>
                    `;

                } catch (error) {
                    document.getElementById('bonusesDetailTest').innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-times-circle me-2"></i>
                            خطأ في اختبار الحوافز المشروطة: ${error.message}
                        </div>
                    `;
                }
            }, 1000);
        }

        function testDeductionsDetail() {
            document.getElementById('deductionsDetailTest').innerHTML = `
                <div class="alert alert-info">
                    <div class="spinner-border spinner-border-sm me-2"></div>
                    جاري اختبار الخصومات الشاملة...
                </div>
            `;

            setTimeout(() => {
                try {
                    let testsPassed = 0;
                    let totalTests = 6;
                    let results = [];

                    // Test mandatory deductions
                    if (payrollManager && typeof payrollManager.getMandatoryDeductions === 'function') {
                        testsPassed++;
                        results.push('✅ دالة حساب الخصومات الإجبارية متاحة');
                    } else {
                        results.push('❌ دالة حساب الخصومات الإجبارية غير متاحة');
                    }

                    // Test advances deductions
                    if (payrollManager && typeof payrollManager.getActiveAdvances === 'function') {
                        testsPassed++;
                        results.push('✅ دالة حساب خصومات السلف متاحة');
                    } else {
                        results.push('❌ دالة حساب خصومات السلف غير متاحة');
                    }

                    // Test penalties deductions
                    if (payrollManager && typeof payrollManager.getFinancialPenalties === 'function') {
                        testsPassed++;
                        results.push('✅ دالة حساب الجزاءات المالية متاحة');
                    } else {
                        results.push('❌ دالة حساب الجزاءات المالية غير متاحة');
                    }

                    // Test attendance deductions
                    if (payrollManager && typeof payrollManager.getOtherDeductions === 'function') {
                        testsPassed++;
                        results.push('✅ دالة حساب خصومات الحضور متاحة');
                    } else {
                        results.push('❌ دالة حساب خصومات الحضور غير متاحة');
                    }

                    // Test helper functions
                    if (payrollManager && typeof payrollManager.calculateMonthlyInstallment === 'function') {
                        testsPassed++;
                        results.push('✅ دالة حساب الأقساط الشهرية متاحة');
                    } else {
                        results.push('❌ دالة حساب الأقساط الشهرية غير متاحة');
                    }

                    // Test total calculations
                    if (payrollManager && typeof payrollManager.calculateDetailedTotals === 'function') {
                        testsPassed++;
                        results.push('✅ دالة حساب الإجماليات التفصيلية متاحة');
                    } else {
                        results.push('❌ دالة حساب الإجماليات التفصيلية غير متاحة');
                    }

                    const alertClass = testsPassed === totalTests ? 'success' :
                                     testsPassed >= totalTests / 2 ? 'warning' : 'danger';

                    document.getElementById('deductionsDetailTest').innerHTML = `
                        <div class="alert alert-${alertClass}">
                            <h6><i class="fas fa-${testsPassed === totalTests ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
                            نتيجة اختبار الخصومات الشاملة: ${testsPassed}/${totalTests}</h6>
                            ${results.map(r => `<small>${r}</small>`).join('<br>')}
                            <hr>
                            <small><strong>أنواع الخصومات المدعومة:</strong><br>
                            • التأمينات الاجتماعية (9%)<br>
                            • ضريبة الدخل المتدرجة<br>
                            • أقساط السلف الشهرية<br>
                            • الجزاءات المالية<br>
                            • خصومات التأخير والغياب والانصراف المبكر<br>
                            • خصومات أخرى</small>
                        </div>
                    `;

                } catch (error) {
                    document.getElementById('deductionsDetailTest').innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-times-circle me-2"></i>
                            خطأ في اختبار الخصومات الشاملة: ${error.message}
                        </div>
                    `;
                }
            }, 1000);
        }

        function testDetailedView() {
            document.getElementById('detailedViewTest').innerHTML = `
                <div class="alert alert-info">
                    <div class="spinner-border spinner-border-sm me-2"></div>
                    جاري اختبار العرض المفصل...
                </div>
            `;

            setTimeout(() => {
                try {
                    let testsPassed = 0;
                    let totalTests = 5;
                    let results = [];

                    // Test detailed view function
                    if (payrollManager && typeof payrollManager.showDetailedPayrollModal === 'function') {
                        testsPassed++;
                        results.push('✅ دالة العرض المفصل متاحة');
                    } else {
                        results.push('❌ دالة العرض المفصل غير متاحة');
                    }

                    // Test HTML generation functions
                    if (payrollManager && typeof payrollManager.generateDetailedPayrollHTML === 'function') {
                        testsPassed++;
                        results.push('✅ دالة إنشاء HTML المفصل متاحة');
                    } else {
                        results.push('❌ دالة إنشاء HTML المفصل غير متاحة');
                    }

                    // Test section generators
                    if (payrollManager && typeof payrollManager.generateAllowancesSection === 'function') {
                        testsPassed++;
                        results.push('✅ دالة إنشاء قسم البدلات متاحة');
                    } else {
                        results.push('❌ دالة إنشاء قسم البدلات غير متاحة');
                    }

                    // Test print function
                    if (payrollManager && typeof payrollManager.printDetailedPayslip === 'function') {
                        testsPassed++;
                        results.push('✅ دالة الطباعة المفصلة متاحة');
                    } else {
                        results.push('❌ دالة الطباعة المفصلة غير متاحة');
                    }

                    // Test export function
                    if (payrollManager && typeof payrollManager.exportDetailedPayslip === 'function') {
                        testsPassed++;
                        results.push('✅ دالة التصدير المفصل متاحة');
                    } else {
                        results.push('❌ دالة التصدير المفصل غير متاحة');
                    }

                    const alertClass = testsPassed === totalTests ? 'success' :
                                     testsPassed >= totalTests / 2 ? 'warning' : 'danger';

                    document.getElementById('detailedViewTest').innerHTML = `
                        <div class="alert alert-${alertClass}">
                            <h6><i class="fas fa-${testsPassed === totalTests ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
                            نتيجة اختبار العرض المفصل: ${testsPassed}/${totalTests}</h6>
                            ${results.map(r => `<small>${r}</small>`).join('<br>')}
                            <hr>
                            <div class="text-center">
                                <button class="btn btn-sm btn-outline-primary" onclick="window.open('payroll.html', '_blank')">
                                    <i class="fas fa-external-link-alt me-1"></i>
                                    فتح صفحة إدارة الرواتب لتجربة النظام
                                </button>
                            </div>
                        </div>
                    `;

                } catch (error) {
                    document.getElementById('detailedViewTest').innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-times-circle me-2"></i>
                            خطأ في اختبار العرض المفصل: ${error.message}
                        </div>
                    `;
                }
            }, 1000);
        }
    </script>
</body>
</html>
