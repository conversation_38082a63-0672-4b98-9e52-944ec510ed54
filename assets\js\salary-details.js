/**
 * نظام بيانات الراتب المفصل المحسن
 * يحسب الاستحقاقات والاستقطاعات بناءً على سجل الحضور والانصراف
 */
class SalaryDetailsManager {
    constructor() {
        this.currentMonth = new Date().toISOString().slice(0, 7);
        this.selectedEmployee = '';
        this.selectedDepartment = '';
        this.calculationCache = new Map();
        this.lastUpdateTime = null;
    }

    init() {
        this.loadSalaryDetailsPage();
        this.bindEvents();
        this.loadSalaryDetails();
        this.updateSummaryCards();
        this.setupAutoRefresh();
    }

    loadSalaryDetailsPage() {
        const content = document.getElementById('contentArea');
        content.innerHTML = `
            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-chart-line me-2"></i>بيانات الراتب المفصل</h2>
                    <div class="btn-group">
                        <button class="btn btn-outline-primary" id="refreshDataBtn">
                            <i class="fas fa-sync-alt"></i> تحديث البيانات
                        </button>
                        <button class="btn btn-outline-success" id="exportDetailsBtn">
                            <i class="fas fa-download"></i> تصدير
                        </button>
                        <button class="btn btn-outline-info" id="printDetailsBtn">
                            <i class="fas fa-print"></i> طباعة
                        </button>
                    </div>
                </div>

                <!-- Filters -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <label class="form-label">الشهر</label>
                                <input type="month" class="form-control" id="monthFilter" value="${this.currentMonth}">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">الموظف</label>
                                <select class="form-select" id="employeeFilter">
                                    <option value="">جميع الموظفين</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">القسم</label>
                                <select class="form-select" id="departmentFilter">
                                    <option value="">جميع الأقسام</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Summary Cards -->
                <div class="row mb-4" id="summaryCards">
                    <!-- Summary cards will be loaded here -->
                </div>

                <!-- Detailed Report -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-table me-2"></i>
                            تفاصيل الرواتب - ${this.formatMonth(this.currentMonth)}
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover table-sm">
                                <thead class="table-dark">
                                    <tr>
                                        <th rowspan="2">الموظف</th>
                                        <th rowspan="2">القسم</th>
                                        <th rowspan="2">أيام العمل</th>
                                        <th rowspan="2">أيام الحضور</th>
                                        <th colspan="5" class="text-center">الاستحقاقات</th>
                                        <th colspan="7" class="text-center">الخصومات</th>
                                        <th rowspan="2">صافي الراتب</th>
                                    </tr>
                                    <tr>
                                        <th>الراتب الأساسي</th>
                                        <th>البدلات</th>
                                        <th>الإضافي</th>
                                        <th>الحوافز</th>
                                        <th>الإجمالي</th>
                                        <th>التأمينات</th>
                                        <th>الضرائب</th>
                                        <th>السلف</th>
                                        <th>التأخير</th>
                                        <th>الغياب</th>
                                        <th>الجزاءات</th>
                                        <th>الإجمالي</th>
                                    </tr>
                                </thead>
                                <tbody id="salaryDetailsBody">
                                    <!-- Salary details will be loaded here -->
                                </tbody>
                                <tfoot id="salaryDetailsTotals">
                                    <!-- Totals will be loaded here -->
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    bindEvents() {
        // Month filter
        document.getElementById('monthFilter').addEventListener('change', (e) => {
            this.currentMonth = e.target.value;
            this.clearCache();
            this.loadSalaryDetails();
            this.updateSummaryCards();
        });

        // Employee filter
        document.getElementById('employeeFilter').addEventListener('change', (e) => {
            this.selectedEmployee = e.target.value;
            this.loadSalaryDetails();
            this.updateSummaryCards();
        });

        // Department filter
        document.getElementById('departmentFilter').addEventListener('change', (e) => {
            this.selectedDepartment = e.target.value;
            this.loadSalaryDetails();
            this.updateSummaryCards();
        });

        // Refresh button
        document.getElementById('refreshDataBtn').addEventListener('click', () => {
            this.clearCache();
            this.loadSalaryDetails();
            this.updateSummaryCards();
            window.samApp.showAlert('تم تحديث البيانات بنجاح', 'success');
        });

        // Export button
        document.getElementById('exportDetailsBtn').addEventListener('click', () => {
            this.exportSalaryDetails();
        });

        // Print button
        document.getElementById('printDetailsBtn').addEventListener('click', () => {
            this.printDetailedReport();
        });
    }

    setupAutoRefresh() {
        // تحديث البيانات كل 5 دقائق
        setInterval(() => {
            if (this.shouldAutoRefresh()) {
                this.refreshSalaryData();
            }
        }, 300000); // 5 minutes

        // الاستماع لتحديثات الحضور
        this.setupAttendanceListener();
    }

    setupAttendanceListener() {
        // الاستماع لأحداث تحديث الحضور
        document.addEventListener('attendanceUpdated', (event) => {
            const { employeeId, date, month } = event.detail || {};
            console.log(`تم تحديث الحضور للموظف ${employeeId} في تاريخ ${date}`);

            // إذا كان التحديث للشهر الحالي، قم بتحديث البيانات
            if (month === this.currentMonth) {
                this.refreshEmployeeData(employeeId);
            }
        });

        // الاستماع لتحديثات الحضور المجمعة
        document.addEventListener('attendanceBulkUpdated', (event) => {
            const { month } = event.detail || {};
            console.log(`تحديث مجمع للحضور للشهر ${month}`);

            if (month === this.currentMonth) {
                this.refreshSalaryData();
            }
        });
    }

    refreshSalaryData() {
        console.log('تحديث جميع بيانات الراتب...');
        this.clearCache();
        this.loadSalaryDetails();
        this.updateSummaryCards();
    }

    refreshEmployeeData(employeeId) {
        console.log(`تحديث بيانات الموظف ${employeeId}...`);

        // مسح الكاش للموظف المحدد
        const cacheKey = `${employeeId}_${this.currentMonth}`;
        this.calculationCache.delete(cacheKey);

        // إعادة حساب وتحديث الصف المحدد
        const calculations = this.calculateEmployeeSalaryDetails(employeeId, this.currentMonth);
        if (calculations) {
            this.updateEmployeeRow(employeeId, calculations);
            this.updateSummaryCards();
        }
    }

    updateEmployeeRow(employeeId, calculations) {
        const employee = Database.getEmployee(employeeId);
        if (!employee) return;

        const departments = Database.getAll('departments') || [];
        const department = departments.find(d => d.id === employee.department);

        // البحث عن الصف في الجدول
        const tableBody = document.getElementById('salaryDetailsBody');
        const rows = tableBody.querySelectorAll('tr');

        for (let row of rows) {
            const employeeCell = row.querySelector('td:first-child');
            if (employeeCell && employeeCell.textContent.includes(employee.name)) {
                // تحديث الصف بالبيانات الجديدة
                row.innerHTML = this.createEmployeeRowHTML(employee, department, calculations);

                // إضافة تأثير بصري للتحديث
                row.classList.add('table-warning');
                setTimeout(() => {
                    row.classList.remove('table-warning');
                    row.classList.add('table-success');
                    setTimeout(() => {
                        row.classList.remove('table-success');
                    }, 2000);
                }, 500);

                break;
            }
        }
    }

    createEmployeeRowHTML(employee, department, calculations) {
        const calc = calculations;

        return `
            <td>
                <div class="d-flex align-items-center">
                    <img src="${employee.photo || 'https://via.placeholder.com/40x40/007bff/ffffff?text=صورة'}"
                         alt="${employee.name}" class="rounded-circle me-2" width="40" height="40">
                    <div>
                        <div class="fw-bold">${employee.name}</div>
                        <small class="text-muted">${employee.employee_number}</small>
                    </div>
                </div>
            </td>
            <td>${department?.name || 'غير محدد'}</td>
            <td class="text-center">${calc.basicInfo.workingDays}</td>
            <td class="text-center">${calc.basicInfo.attendanceDays}</td>
            <td class="text-end">${window.samApp.formatCurrency(calc.basicInfo.proportionalSalary)}</td>
            <td class="text-end text-success">${window.samApp.formatCurrency(calc.allowances.total)}</td>
            <td class="text-end text-info">${window.samApp.formatCurrency(calc.bonuses.total)}</td>
            <td class="text-end text-warning">${calc.bonuses.overtime.hours > 0 ? calc.bonuses.overtime.hours + 'س' : '-'}</td>
            <td class="text-end text-primary">${window.samApp.formatCurrency(calc.totals.grossSalary)}</td>
            <td class="text-end text-danger">${window.samApp.formatCurrency(calc.deductions.socialInsurance)}</td>
            <td class="text-end text-danger">${window.samApp.formatCurrency(calc.deductions.incomeTax)}</td>
            <td class="text-end text-danger">${window.samApp.formatCurrency(calc.deductions.advances)}</td>
            <td class="text-end text-danger">${window.samApp.formatCurrency(calc.deductions.lateness)}</td>
            <td class="text-end text-danger">${window.samApp.formatCurrency(calc.deductions.absence)}</td>
            <td class="text-end text-danger">${window.samApp.formatCurrency(calc.deductions.penalties)}</td>
            <td class="text-end text-danger">${window.samApp.formatCurrency(calc.deductions.total)}</td>
            <td class="text-end"><strong class="text-success">${window.samApp.formatCurrency(calc.totals.netSalary)}</strong></td>
        `;
    }

    shouldAutoRefresh() {
        // تحديث تلقائي فقط إذا كان الشهر الحالي
        const currentMonth = new Date().toISOString().slice(0, 7);
        return this.currentMonth === currentMonth;
    }

    clearCache() {
        this.calculationCache.clear();
        this.lastUpdateTime = new Date();
    }

    loadEmployeeOptions() {
        const employees = Database.getEmployees().filter(emp => emp.status === 'active');
        const departments = Database.getAll('departments') || [];
        
        // Load employee options
        const employeeSelect = document.getElementById('employeeFilter');
        employeeSelect.innerHTML = '<option value="">جميع الموظفين</option>';
        employees.forEach(emp => {
            employeeSelect.innerHTML += `<option value="${emp.id}">${emp.name} (${emp.employee_number})</option>`;
        });

        // Load department options
        const departmentSelect = document.getElementById('departmentFilter');
        departmentSelect.innerHTML = '<option value="">جميع الأقسام</option>';
        departments.forEach(dept => {
            departmentSelect.innerHTML += `<option value="${dept.id}">${dept.name}</option>`;
        });
    }

    loadSalaryDetails() {
        try {
            // إظهار مؤشر التحميل
            const tbody = document.getElementById('salaryDetailsBody');
            tbody.innerHTML = `
                <tr>
                    <td colspan="17" class="text-center py-4">
                        <div class="spinner-border text-primary me-2" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                        <span>جاري تحميل بيانات الراتب...</span>
                    </td>
                </tr>
            `;

            this.loadEmployeeOptions();

            const employees = Database.getEmployees().filter(emp => emp.status === 'active');
            let salaryData = [];
            let processedCount = 0;

            console.log(`Processing ${employees.length} employees for month ${this.currentMonth}`);

            employees.forEach(employee => {
                try {
                    // Apply filters
                    if (this.selectedEmployee && employee.id !== this.selectedEmployee) return;
                    if (this.selectedDepartment && employee.department !== this.selectedDepartment) return;

                    console.log(`Processing employee: ${employee.name} (${employee.id})`);

                    const calculations = this.calculateEmployeeSalaryDetails(employee.id, this.currentMonth);

                    if (calculations) {
                        salaryData.push({
                            employee: employee,
                            calculations: calculations
                        });
                        processedCount++;
                    } else {
                        console.warn(`No calculations returned for employee: ${employee.name}`);
                    }
                } catch (error) {
                    console.error(`Error processing employee ${employee.name}:`, error);
                }
            });

            console.log(`Successfully processed ${processedCount} employees`);

            // تأخير قصير لإظهار مؤشر التحميل
            setTimeout(() => {
                this.renderSalaryDetails(salaryData);
                this.renderSalaryTotals(salaryData);
            }, 500);

        } catch (error) {
            console.error('Error loading salary details:', error);
            const tbody = document.getElementById('salaryDetailsBody');
            tbody.innerHTML = `
                <tr>
                    <td colspan="17" class="text-center py-4">
                        <i class="fas fa-exclamation-triangle fa-2x text-danger mb-2"></i>
                        <p class="text-danger mb-0">حدث خطأ في تحميل بيانات الراتب</p>
                        <small class="text-muted">${error.message}</small>
                    </td>
                </tr>
            `;
        }
    }

    renderSalaryDetails(salaryData) {
        const tbody = document.getElementById('salaryDetailsBody');
        const departments = Database.getAll('departments') || [];

        if (salaryData.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="15" class="text-center py-4">
                        <i class="fas fa-chart-line fa-2x text-muted mb-2"></i>
                        <p class="text-muted mb-0">لا توجد بيانات راتب للعرض</p>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = salaryData.map(item => {
            const employee = item.employee;
            const calc = item.calculations;
            const department = departments.find(d => d.id === employee.department);

            return `
                <tr>
                    <td>
                        <div class="d-flex align-items-center">
                            <img src="${employee.photo || 'https://via.placeholder.com/40x40/007bff/ffffff?text=صورة'}"
                                 alt="${employee.name}" class="rounded-circle me-2" width="40" height="40">
                            <div>
                                <div class="fw-bold">${employee.name}</div>
                                <small class="text-muted">${employee.employee_number}</small>
                            </div>
                        </div>
                    </td>
                    <td>${department?.name || 'غير محدد'}</td>
                    <td class="text-center">${calc.basicInfo.workingDays}</td>
                    <td class="text-center">${calc.basicInfo.attendanceDays}</td>
                    <td class="text-end">${window.samApp.formatCurrency(calc.basicInfo.proportionalSalary)}</td>
                    <td class="text-end text-success">${window.samApp.formatCurrency(calc.allowances.total)}</td>
                    <td class="text-end text-info">${window.samApp.formatCurrency(calc.bonuses.total)}</td>
                    <td class="text-end text-warning">${calc.bonuses.overtime.hours > 0 ? calc.bonuses.overtime.hours + 'س' : '-'}</td>
                    <td class="text-end text-primary">${window.samApp.formatCurrency(calc.totals.grossSalary)}</td>
                    <td class="text-end text-danger">${window.samApp.formatCurrency(calc.deductions.socialInsurance)}</td>
                    <td class="text-end text-danger">${window.samApp.formatCurrency(calc.deductions.incomeTax)}</td>
                    <td class="text-end text-danger">${window.samApp.formatCurrency(calc.deductions.advances)}</td>
                    <td class="text-end text-danger">${window.samApp.formatCurrency(calc.deductions.lateness)}</td>
                    <td class="text-end text-danger">${window.samApp.formatCurrency(calc.deductions.absence)}</td>
                    <td class="text-end text-danger">${window.samApp.formatCurrency(calc.deductions.penalties)}</td>
                    <td class="text-end text-danger">${window.samApp.formatCurrency(calc.deductions.total)}</td>
                    <td class="text-end"><strong class="text-success">${window.samApp.formatCurrency(calc.totals.netSalary)}</strong></td>
                </tr>
            `;
        }).join('');
    }

    renderSalaryTotals(salaryData) {
        const tfoot = document.getElementById('salaryDetailsTotals');

        if (salaryData.length === 0) {
            tfoot.innerHTML = '';
            return;
        }

        // Calculate totals
        const totals = salaryData.reduce((acc, item) => {
            const calc = item.calculations;
            acc.employees += 1;
            acc.workingDays += calc.basicInfo.workingDays;
            acc.attendanceDays += calc.basicInfo.attendanceDays;
            acc.proportionalSalary += calc.basicInfo.proportionalSalary;
            acc.allowances += calc.allowances.total;
            acc.bonuses += calc.bonuses.total;
            acc.overtimeHours += calc.bonuses.overtime.hours;
            acc.grossSalary += calc.totals.grossSalary;
            acc.socialInsurance += calc.deductions.socialInsurance;
            acc.incomeTax += calc.deductions.incomeTax;
            acc.advances += calc.deductions.advances;
            acc.lateness += calc.deductions.lateness;
            acc.absence += calc.deductions.absence;
            acc.penalties += calc.deductions.penalties;
            acc.totalDeductions += calc.deductions.total;
            acc.netSalary += calc.totals.netSalary;
            return acc;
        }, {
            employees: 0, workingDays: 0, attendanceDays: 0, proportionalSalary: 0,
            allowances: 0, bonuses: 0, overtimeHours: 0, grossSalary: 0,
            socialInsurance: 0, incomeTax: 0, advances: 0, lateness: 0,
            absence: 0, penalties: 0, totalDeductions: 0, netSalary: 0
        });

        tfoot.innerHTML = `
            <tr class="table-warning fw-bold">
                <td>الإجمالي (${totals.employees} موظف)</td>
                <td>-</td>
                <td class="text-center">${Math.round(totals.workingDays / totals.employees)}</td>
                <td class="text-center">${Math.round(totals.attendanceDays / totals.employees)}</td>
                <td class="text-end">${window.samApp.formatCurrency(totals.proportionalSalary)}</td>
                <td class="text-end">${window.samApp.formatCurrency(totals.allowances)}</td>
                <td class="text-end">${window.samApp.formatCurrency(totals.bonuses)}</td>
                <td class="text-end">${totals.overtimeHours.toFixed(1)}س</td>
                <td class="text-end">${window.samApp.formatCurrency(totals.grossSalary)}</td>
                <td class="text-end">${window.samApp.formatCurrency(totals.socialInsurance)}</td>
                <td class="text-end">${window.samApp.formatCurrency(totals.incomeTax)}</td>
                <td class="text-end">${window.samApp.formatCurrency(totals.advances)}</td>
                <td class="text-end">${window.samApp.formatCurrency(totals.lateness)}</td>
                <td class="text-end">${window.samApp.formatCurrency(totals.absence)}</td>
                <td class="text-end">${window.samApp.formatCurrency(totals.penalties)}</td>
                <td class="text-end">${window.samApp.formatCurrency(totals.totalDeductions)}</td>
                <td class="text-end">${window.samApp.formatCurrency(totals.netSalary)}</td>
            </tr>
        `;
    }

    updateSummaryCards() {
        const employees = Database.getEmployees().filter(emp => emp.status === 'active');
        let totalBasicSalary = 0;
        let totalAllowances = 0;
        let totalBonuses = 0;
        let totalDeductions = 0;
        let totalNetSalary = 0;
        let employeeCount = 0;

        employees.forEach(employee => {
            if (this.selectedEmployee && employee.id !== this.selectedEmployee) return;
            if (this.selectedDepartment && employee.department !== this.selectedDepartment) return;

            const calculations = this.calculateEmployeeSalaryDetails(employee.id, this.currentMonth);

            if (calculations) {
                employeeCount++;

                // Use basic salary (not proportional) for the total basic salary calculation
                const basicSalary = calculations.basicInfo.basicSalary || 0;
                const allowances = calculations.allowances.total || 0;
                const bonuses = calculations.bonuses.total || 0;
                const deductions = calculations.deductions.total || 0;
                const netSalary = calculations.totals.netSalary || 0;

                totalBasicSalary += basicSalary;
                totalAllowances += allowances;
                totalBonuses += bonuses;
                totalDeductions += deductions;
                totalNetSalary += netSalary;

                console.log(`Employee ${employee.name}: Basic Salary: ${basicSalary}, Total Basic So Far: ${totalBasicSalary}`);
            }
        });

        // Update summary cards
        const summaryCards = document.getElementById('summaryCards');
        summaryCards.innerHTML = `
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">عدد الموظفين</h6>
                                <h4>${employeeCount}</h4>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-users fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">إجمالي الرواتب الأساسية</h6>
                                <h4>${window.samApp.formatCurrency(totalBasicSalary)}</h4>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-money-bill-wave fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">إجمالي البدلات والحوافز</h6>
                                <h4>${window.samApp.formatCurrency(totalAllowances + totalBonuses)}</h4>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-plus-circle fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">صافي الرواتب</h6>
                                <h4>${window.samApp.formatCurrency(totalNetSalary)}</h4>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-calculator fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    exportSalaryDetails() {
        const employees = Database.getEmployees().filter(emp => emp.status === 'active');
        let exportData = [];

        employees.forEach(employee => {
            if (this.selectedEmployee && employee.id !== this.selectedEmployee) return;
            if (this.selectedDepartment && employee.department !== this.selectedDepartment) return;

            const calculations = this.calculateEmployeeSalaryDetails(employee.id, this.currentMonth);
            const departments = Database.getAll('departments') || [];
            const department = departments.find(d => d.id === employee.department);

            if (calculations) {
                exportData.push({
                    'الموظف': employee.name,
                    'الرقم الوظيفي': employee.employee_number,
                    'القسم': department?.name || 'غير محدد',
                    'أيام العمل': calculations.basicInfo.workingDays,
                    'أيام الحضور': calculations.basicInfo.attendanceDays,
                    'الراتب الأساسي': calculations.basicInfo.proportionalSalary,
                    'البدلات': calculations.allowances.total,
                    'الساعات الإضافية': calculations.bonuses.overtime.hours,
                    'قيمة الإضافي': calculations.bonuses.overtime.amount,
                    'الحوافز': calculations.bonuses.performance + calculations.bonuses.other,
                    'إجمالي الاستحقاقات': calculations.totals.grossSalary,
                    'التأمينات الاجتماعية': calculations.deductions.socialInsurance,
                    'ضريبة الدخل': calculations.deductions.incomeTax,
                    'السلف': calculations.deductions.advances,
                    'خصم التأخير': calculations.deductions.lateness,
                    'خصم الغياب': calculations.deductions.absence,
                    'الجزاءات': calculations.deductions.penalties,
                    'إجمالي الخصومات': calculations.deductions.total,
                    'صافي الراتب': calculations.totals.netSalary
                });
            }
        });

        if (exportData.length === 0) {
            window.samApp.showAlert('لا توجد بيانات للتصدير', 'warning');
            return;
        }

        const ws = XLSX.utils.json_to_sheet(exportData);
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, 'بيانات الراتب المفصل');

        const fileName = `salary_details_${this.currentMonth}${this.selectedEmployee ? '_' + this.selectedEmployee : ''}.xlsx`;
        XLSX.writeFile(wb, fileName);

        window.samApp.showAlert(`تم تصدير ${exportData.length} سجل بنجاح`, 'success');
    }

    printDetailedReport() {
        const tableContent = document.querySelector('#salaryDetailsBody').parentElement.outerHTML;
        const title = `تقرير بيانات الراتب المفصل - ${this.formatMonth(this.currentMonth)}`;

        let filterInfo = '';
        if (this.selectedEmployee) {
            const employee = Database.getEmployee(this.selectedEmployee);
            filterInfo += `الموظف: ${employee?.name || 'غير معروف'} - `;
        }
        if (this.selectedDepartment) {
            const departments = Database.getAll('departments') || [];
            const department = departments.find(d => d.id === this.selectedDepartment);
            filterInfo += `القسم: ${department?.name || 'غير معروف'} - `;
        }
        filterInfo += `الشهر: ${this.formatMonth(this.currentMonth)}`;

        window.printManager.printReport(title, tableContent, {
            showDate: true,
            showCompanyInfo: true,
            subtitle: filterInfo
        });
    }

    calculateEmployeeSalaryDetails(employeeId, month) {
        try {
            // Check cache first
            const cacheKey = `${employeeId}_${month}`;
            if (this.calculationCache.has(cacheKey)) {
                return this.calculationCache.get(cacheKey);
            }

            const employee = Database.getEmployee(employeeId);
            if (!employee) {
                console.warn(`Employee not found: ${employeeId}`);
                return null;
            }

            // Get attendance data for the month
            const attendance = Database.getAttendance({ employee_id: employeeId, month: month }) || [];

            // Calculate working days and actual attendance days from records
            const workingDays = this.getWorkingDaysInMonth(month, employee);
            const attendanceStats = this.calculateAttendanceStats(attendance, workingDays, month, employee);

            console.log(`Employee ${employee.name}: Working days: ${workingDays}, Present: ${attendanceStats.presentDays}, Absent: ${attendanceStats.absentDays}, Records: ${attendance.length}`);

            const attendanceDays = attendanceStats.presentDays;
            const absentDays = attendanceStats.absentDays;

            // Basic salary calculations
            const basicSalary = employee.salary || 0;
            const proportionalSalary = workingDays > 0 ? (basicSalary * attendanceDays / workingDays) : basicSalary;

        // Calculate detailed allowances and deductions using overtime calculator
        let overtimeCalculations = null;
        if (window.overtimeCalculator) {
            overtimeCalculations = window.overtimeCalculator.calculateAllowancesAndDeductions(employeeId, month);
        }

        const calculations = {
            basicInfo: {
                workingDays,
                attendanceDays,
                absentDays,
                basicSalary,
                proportionalSalary: Math.round(proportionalSalary * 100) / 100
            },
            allowances: this.calculateAllowances(employee, overtimeCalculations),
            bonuses: this.calculateBonuses(employee, overtimeCalculations, attendance),
            deductions: this.calculateDeductions(employee, overtimeCalculations, attendance, basicSalary),
            totals: {}
        };

        // Calculate totals
        calculations.totals.grossSalary = Math.round((calculations.basicInfo.proportionalSalary +
                                        calculations.allowances.total +
                                        calculations.bonuses.total) * 100) / 100;

        calculations.totals.totalDeductions = calculations.deductions.total;
        calculations.totals.netSalary = Math.max(0, Math.round((calculations.totals.grossSalary - calculations.totals.totalDeductions) * 100) / 100);

            // Cache the result
            this.calculationCache.set(cacheKey, calculations);

            return calculations;

        } catch (error) {
            console.error(`Error calculating salary details for employee ${employeeId}:`, error);

            // Return basic calculation as fallback
            const employee = Database.getEmployee(employeeId);
            if (employee) {
                const basicSalary = employee.salary || 0;
                return {
                    basicInfo: {
                        workingDays: 22,
                        attendanceDays: 22,
                        absentDays: 0,
                        basicSalary: basicSalary,
                        proportionalSalary: basicSalary
                    },
                    allowances: { housing: 0, transport: 0, food: 0, other: 0, total: 0 },
                    bonuses: { overtime: { hours: 0, amount: 0 }, performance: 0, other: 0, total: 0 },
                    deductions: {
                        socialInsurance: basicSalary * 0.09,
                        incomeTax: this.calculateIncomeTax(basicSalary),
                        advances: 0, lateness: 0, absence: 0, penalties: 0,
                        total: (basicSalary * 0.09) + this.calculateIncomeTax(basicSalary)
                    },
                    totals: {
                        grossSalary: basicSalary,
                        totalDeductions: (basicSalary * 0.09) + this.calculateIncomeTax(basicSalary),
                        netSalary: basicSalary - ((basicSalary * 0.09) + this.calculateIncomeTax(basicSalary))
                    }
                };
            }

            return null;
        }
    }

    calculateAttendanceStats(attendance, workingDays, month, employee) {
        // Create a map of all working days in the month
        const [year, monthNum] = month.split('-');
        const workingDaysMap = this.getWorkingDaysMap(year, monthNum, employee);

        let presentDays = 0;
        let absentDays = 0;
        let lateCount = 0;
        let earlyLeaveCount = 0;

        // Process each working day
        workingDaysMap.forEach((isWorkingDay, dateStr) => {
            if (!isWorkingDay) return; // Skip non-working days

            // Find attendance record for this date
            const record = attendance.find(a => a.date === dateStr);

            if (record) {
                // Check the status of the record
                switch (record.status) {
                    case 'present':
                    case 'late':
                    case 'early_leave':
                        presentDays++;
                        if (record.status === 'late') lateCount++;
                        if (record.status === 'early_leave') earlyLeaveCount++;
                        break;
                    case 'absent':
                    case 'unauthorized_absence':
                        absentDays++;
                        break;
                    case 'vacation':
                    case 'sick_leave':
                    case 'authorized_leave':
                        // Count as present for salary calculation purposes
                        presentDays++;
                        break;
                    default:
                        // If check_in exists, consider as present
                        if (record.check_in) {
                            presentDays++;
                        } else {
                            absentDays++;
                        }
                        break;
                }
            } else {
                // No record found for this working day = absent
                absentDays++;
            }
        });

        return {
            presentDays,
            absentDays,
            lateCount,
            earlyLeaveCount,
            totalWorkingDays: workingDays,
            attendanceRate: workingDays > 0 ? Math.round((presentDays / workingDays) * 100) : 0
        };
    }

    getWorkingDaysMap(year, month, employee = null) {
        const workingDaysMap = new Map();
        const date = new Date(year, month - 1, 1);
        const lastDay = new Date(year, month, 0).getDate();

        // Get working days configuration
        let workingDaysList;
        if (employee && employee.working_days && employee.working_days.length > 0) {
            workingDaysList = employee.working_days;
        } else {
            const settings = Database.getSettings();
            workingDaysList = settings.working_hours.working_days || ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday'];
        }

        // Map each day of the month
        for (let day = 1; day <= lastDay; day++) {
            date.setDate(day);
            const dateStr = date.toISOString().split('T')[0];
            const dayName = date.toLocaleDateString('en-US', { weekday: 'lowercase' });
            const isWorkingDay = workingDaysList.includes(dayName);

            workingDaysMap.set(dateStr, isWorkingDay);
        }

        return workingDaysMap;
    }

    calculateAllowances(employee, overtimeCalculations) {
        // Use overtime calculations if available, otherwise use employee defaults
        let housing = 0, transport = 0, food = 0, other = 0;

        if (overtimeCalculations?.allowances) {
            housing = overtimeCalculations.allowances.housing || 0;
            transport = overtimeCalculations.allowances.transport || 0;
            food = overtimeCalculations.allowances.food || 0;
            other = overtimeCalculations.allowances.other || 0;
        } else {
            // Fallback to employee basic allowances
            housing = employee.housing_allowance || 0;
            transport = employee.transport_allowance || 0;
            food = employee.food_allowance || 0;
            other = employee.other_allowances || 0;
        }

        return {
            housing: Math.round(housing * 100) / 100,
            transport: Math.round(transport * 100) / 100,
            food: Math.round(food * 100) / 100,
            other: Math.round(other * 100) / 100,
            total: Math.round((housing + transport + food + other) * 100) / 100
        };
    }

    calculateBonuses(employee, overtimeCalculations, attendance) {
        let overtimeHours = 0, overtimeAmount = 0, performance = 0, other = 0;

        if (overtimeCalculations?.details?.overtime) {
            overtimeHours = overtimeCalculations.details.overtime.hours || 0;
            overtimeAmount = overtimeCalculations.details.overtime.amount || 0;
        }

        if (overtimeCalculations?.bonuses) {
            performance = overtimeCalculations.bonuses.performance || 0;
            other = overtimeCalculations.bonuses.other || 0;
        }

        // If no overtime calculations, calculate basic overtime from attendance
        if (!overtimeCalculations && attendance && attendance.length > 0) {
            const totalOvertimeMinutes = attendance.reduce((total, record) => {
                return total + (record.overtime_minutes || 0);
            }, 0);

            overtimeHours = totalOvertimeMinutes / 60;

            // Calculate overtime pay using basic rate
            const basicSalary = employee.salary || 0;
            const hourlyRate = basicSalary / (22 * 8); // Assuming 22 working days, 8 hours per day
            overtimeAmount = overtimeHours * hourlyRate * 1.5; // 1.5x rate for overtime
        }

        return {
            overtime: {
                hours: Math.round(overtimeHours * 100) / 100,
                amount: Math.round(overtimeAmount * 100) / 100
            },
            performance: Math.round(performance * 100) / 100,
            other: Math.round(other * 100) / 100,
            total: Math.round((overtimeAmount + performance + other) * 100) / 100
        };
    }

    calculateDeductions(employee, overtimeCalculations, attendance, basicSalary) {
        const socialInsurance = basicSalary * 0.09; // 9%
        const incomeTax = this.calculateIncomeTax(basicSalary);

        let advances = 0, lateness = 0, absence = 0, penalties = 0;

        if (overtimeCalculations?.deductions) {
            advances = overtimeCalculations.deductions.advances || 0;
            lateness = overtimeCalculations.deductions.lateness || 0;
            absence = overtimeCalculations.deductions.absence || 0;
            penalties = overtimeCalculations.deductions.penalties || 0;
        } else {
            // Calculate basic deductions from attendance if no overtime calculations
            if (attendance && attendance.length > 0) {
                // Calculate lateness deduction
                const totalLateMinutes = attendance.reduce((total, record) => {
                    return total + (record.late_minutes || 0);
                }, 0);

                // Deduct for lateness (e.g., 1 hour salary per 60 minutes late)
                const hourlyRate = basicSalary / (22 * 8);
                lateness = (totalLateMinutes / 60) * hourlyRate;

                // Calculate absence deduction
                const absentDays = attendance.filter(a => a.status === 'absent').length;
                const dailyRate = basicSalary / 22;
                absence = absentDays * dailyRate;
            }
        }

        return {
            socialInsurance: Math.round(socialInsurance * 100) / 100,
            incomeTax: Math.round(incomeTax * 100) / 100,
            advances: Math.round(advances * 100) / 100,
            lateness: Math.round(lateness * 100) / 100,
            absence: Math.round(absence * 100) / 100,
            penalties: Math.round(penalties * 100) / 100,
            total: Math.round((socialInsurance + incomeTax + advances + lateness + absence + penalties) * 100) / 100
        };
    }

    calculateIncomeTax(salary) {
        // Simple progressive tax calculation
        if (salary <= 3000) return 0;
        if (salary <= 5000) return (salary - 3000) * 0.05;
        if (salary <= 10000) return 2000 * 0.05 + (salary - 5000) * 0.10;
        return 2000 * 0.05 + 5000 * 0.10 + (salary - 10000) * 0.15;
    }

    getWorkingDaysInMonth(monthStr, employee = null) {
        const [year, month] = monthStr.split('-');
        const date = new Date(year, month - 1, 1);
        const lastDay = new Date(year, month, 0).getDate();
        let workingDays = 0;

        // Use employee-specific working days if available, otherwise use global settings
        let workingDaysList;
        if (employee && employee.working_days && employee.working_days.length > 0) {
            workingDaysList = employee.working_days;
        } else {
            const settings = Database.getSettings();
            workingDaysList = settings.working_hours.working_days || ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday'];
        }

        for (let day = 1; day <= lastDay; day++) {
            date.setDate(day);
            const dayName = date.toLocaleDateString('en-US', { weekday: 'lowercase' });
            if (workingDaysList.includes(dayName)) {
                workingDays++;
            }
        }

        return workingDays;
    }

    formatMonth(monthStr) {
        const [year, month] = monthStr.split('-');
        const monthNames = [
            'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
            'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
        ];
        return `${monthNames[parseInt(month) - 1]} ${year}`;
    }
}

// Global reference
let salaryDetailsManager;

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Initialize salary details manager when needed
    if (typeof window.initSalaryDetails === 'undefined') {
        window.initSalaryDetails = function() {
            if (!salaryDetailsManager) {
                salaryDetailsManager = new SalaryDetailsManager();
            }
            salaryDetailsManager.init();
        };
    }
});
